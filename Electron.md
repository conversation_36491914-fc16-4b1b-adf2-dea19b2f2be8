# Tauri Webview Navigation Fix - Complete Solution

## src-tauri/tauri.conf.json

```json
{
  "productName": "switch-ai",
  "version": "0.1.0",
  "identifier": "com.switchai.app",
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "app": {
    "windows": [
      {
        "label": "main",
        "title": "Switch.AI",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600,
        "center": true,
        "resizable": true,
        "fullscreen": false,
        "decorations": true,
        "transparent": false,
        "alwaysOnTop": false,
        "skipTaskbar": false,
        "titleBarStyle": "Visible"
      }
    ],
    "security": {
      "csp": {
        "default-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'", "data:", "https:", "http:", "tauri:", "asset:"],
        "connect-src": ["'self'", "https:", "http:", "wss:", "ws:"],
        "script-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https:", "http:"],
        "style-src": ["'self'", "'unsafe-inline'", "https:", "http:"],
        "img-src": ["'self'", "data:", "https:", "http:", "asset:", "tauri:"],
        "font-src": ["'self'", "data:", "https:", "http:"],
        "media-src": ["'self'", "data:", "https:", "http:"],
        "frame-src": ["'self'", "https:", "http:"]
      },
      "dangerousDisableAssetCspModification": true
    }
  },
  "bundle": {
    "active": true,
    "targets": "all",
    "icon": [
      "icons/32x32.png",
      "icons/128x128.png",
      "icons/<EMAIL>",
      "icons/icon.icns",
      "icons/icon.ico"
    ],
    "resources": [],
    "externalBin": [],
    "copyright": "",
    "category": "DeveloperTool",
    "shortDescription": "",
    "longDescription": "",
    "deb": {
      "depends": []
    },
    "macOS": {
      "frameworks": [],
      "minimumSystemVersion": "",
      "exceptionDomain": "",
      "signingIdentity": null,
      "providerShortName": null,
      "entitlements": null
    },
    "windows": {
      "certificateThumbprint": null,
      "digestAlgorithm": "sha256",
      "timestampUrl": ""
    }
  },
  "permissions": [
    "core:default",
    "opener:default",
    "shell:default",
    {
      "identifier": "opener:allow-open-url",
      "allow": [
        {
          "url": "https://*"
        },
        {
          "url": "http://*"
        }
      ]
    },
    {
      "identifier": "shell:allow-open",
      "allow": [
        {
          "name": "open-url",
          "cmd": "open",
          "args": true
        }
      ]
    }
  ],
  "plugins": {}
}
```

## src-tauri/src/lib.rs

```rust
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, WindowEvent};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .on_window_event(|window, event| {
            match event {
                WindowEvent::Navigation(url) => {
                    // Allow navigation to any HTTPS/HTTP URL
                    if url.scheme() == "https" || url.scheme() == "http" {
                        // Allow navigation
                    } else {
                        // Block other schemes
                        window.prevent_close();
                    }
                }
                _ => {}
            }
        })
        .setup(|app| {
            let main_window = app.get_webview_window("main").unwrap();
            
            // Enable developer tools in debug mode
            #[cfg(debug_assertions)]
            {
                main_window.open_devtools();
            }
            
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

## src-tauri/Cargo.toml

```toml
[package]
name = "switch-ai"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["macos-private-api"] }
tauri-plugin-opener = "2.0"
tauri-plugin-shell = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
```

## src/components/WebView.tsx

```tsx
import { useEffect, useRef } from 'react';
import { useAppStore } from '../store';
import { WebviewWindow, appWindow, LogicalSize, LogicalPosition } from '@tauri-apps/api/window';
import { UnlistenFn } from '@tauri-apps/api/event';
import { open } from '@tauri-apps/plugin-opener';

const webviewInstances = new Map<string, WebviewWindow>();

async function hideAllWebviews() {
  for (const webview of webviewInstances.values()) {
    try {
      await webview.hide();
    } catch (error) {
      console.warn('Failed to hide webview:', error);
    }
  }
}

export const WebView = () => {
  const { activePlatform, activeMode } = useAppStore();
  const previousPlatformId = useRef<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateWebviewGeometry = async () => {
    if (!activePlatform || !containerRef.current) return;
    const webview = webviewInstances.get(activePlatform.id);
    if (!webview) return;

    try {
      const rect = containerRef.current.getBoundingClientRect();
      const size = new LogicalSize(rect.width, rect.height);
      const position = new LogicalPosition(rect.left, rect.top);

      await webview.setSize(size);
      await webview.setPosition(position);
    } catch (error) {
      console.warn('Failed to update webview geometry:', error);
    }
  };

  useEffect(() => {
    const manageWebviews = async () => {
      if (previousPlatformId.current) {
        const prevWebview = webviewInstances.get(previousPlatformId.current);
        if (prevWebview) {
          try {
            await prevWebview.hide();
          } catch (error) {
            console.warn('Failed to hide previous webview:', error);
          }
        }
      }

      if (!activePlatform) {
        previousPlatformId.current = null;
        return;
      }

      const { id, url, name } = activePlatform;
      const label = `platform-${id}`;
      let webview = webviewInstances.get(id);

      if (webview) {
        try {
          await updateWebviewGeometry();
          await webview.show();
          await webview.setFocus();
        } catch (error) {
          console.warn('Failed to show existing webview:', error);
        }
      } else {
        try {
          webview = new WebviewWindow(label, {
            url,
            title: name,
            visible: false,
            decorations: false,
            skipTaskbar: true,
            resizable: true,
            transparent: false,
            alwaysOnTop: false,
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            navigateWithKeyboard: true,
            acceptFirstMouse: true,
            tabbingIdentifier: label,
          });
          
          webviewInstances.set(id, webview);

          webview.once('tauri://created', async () => {
            try {
              await updateWebviewGeometry();
              await webview.show();
              await webview.setFocus();
            } catch (error) {
              console.warn('Failed to setup new webview:', error);
            }
          });

          webview.once('tauri://error', (e) => {
            console.error(`Failed to create webview for ${name}:`, e);
            webviewInstances.delete(id);
          });

          // Handle navigation events
          webview.onNavigation(async (event) => {
            console.log('Navigation event:', event);
            // Allow all navigation for AI platforms
            return true;
          });

        } catch (error) {
          console.error('Failed to create webview:', error);
        }
      }

      previousPlatformId.current = id;
    };

    if (activeMode !== 'settings') {
      manageWebviews();
    }
  }, [activePlatform, activeMode]);

  useEffect(() => {
    if (activeMode === 'settings') {
      hideAllWebviews();
      previousPlatformId.current = null;
    }
  }, [activeMode]);

  useEffect(() => {
    let unlistenResize: UnlistenFn;
    let unlistenMove: UnlistenFn;

    const listenToWindowChanges = async () => {
      try {
        unlistenResize = await appWindow.onResized(updateWebviewGeometry);
        unlistenMove = await appWindow.onMoved(updateWebviewGeometry);
      } catch (error) {
        console.warn('Failed to setup window listeners:', error);
      }
    };

    listenToWindowChanges();

    const timer = setTimeout(() => {
      updateWebviewGeometry().catch(console.warn);
## Alternative Solution: Electron-based Implementation

### package.json (Electron version)

```json
{
  "name": "switch-ai-electron",
  "private": true,
  "version": "0.1.0",
  "type": "module",
  "main": "electron/main.js",
  "scripts": {
    "dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"",
    "dev:vite": "vite",
    "dev:electron": "wait-on http://localhost:1420 && electron .",
    "build": "tsc && vite build && electron-builder",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "zustand": "^5.0.7"
  },
  "devDependencies": {
    "@types/react": "^18.3.1",
    "@types/react-dom": "^18.3.1",
    "@vitejs/plugin-react": "^4.3.4",
    "autoprefixer": "^10.4.21",
    "concurrently": "^8.2.2",
    "electron": "^28.0.0",
    "electron-builder": "^24.6.4",
    "postcss": "^8.5.6",
    "tailwindcss": "^3.4.17",
    "typescript": "~5.6.2",
    "vite": "^6.0.3",
    "wait-on": "^7.2.0"
  }
}
```

### electron/main.js

```javascript
import { app, BrowserWindow, shell, ipcMain, session } from 'electron';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let mainWindow;
const webviewWindows = new Map();

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    titleBarStyle: 'default',
    show: false
  });

  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:1420');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    webviewWindows.forEach(window => window.close());
    webviewWindows.clear();
  });
}

function createWebviewWindow(url, title, id) {
  if (webviewWindows.has(id)) {
    const existingWindow = webviewWindows.get(id);
    existingWindow.show();
    existingWindow.focus();
    return;
  }

  const webviewWindow = new BrowserWindow({
    width: 800,
    height: 600,
    parent: mainWindow,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
      allowRunningInsecureContent: true
    },
    titleBarStyle: 'default',
    frame: false,
    skipTaskbar: true
  });

  webviewWindow.loadURL(url);
  
  webviewWindow.once('ready-to-show', () => {
    webviewWindow.show();
  });

  webviewWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  webviewWindow.on('closed', () => {
    webviewWindows.delete(id);
  });

  webviewWindows.set(id, webviewWindow);
  return webviewWindow;
}

app.whenReady().then(() => {
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ['default-src * data: blob: filesystem: about: ws: wss: \'unsafe-inline\' \'unsafe-eval\'; script-src * data: blob: \'unsafe-inline\' \'unsafe-eval\'; connect-src * data: blob: \'unsafe-inline\'; img-src * data: blob: \'unsafe-inline\'; frame-src * data: blob:; style-src * data: blob: \'unsafe-inline\'; font-src * data: blob: \'unsafe-inline\';']
      }
    });
  });

  createMainWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createMainWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

ipcMain.handle('create-webview', (event, { url, title, id }) => {
  return createWebviewWindow(url, title, id);
});

ipcMain.handle('hide-webview', (event, id) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.hide();
  }
});

ipcMain.handle('show-webview', (event, id) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.show();
    window.focus();
  }
});

ipcMain.handle('close-webview', (event, id) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.close();
  }
});

ipcMain.handle('set-webview-bounds', (event, id, bounds) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.setBounds(bounds);
  }
});
```

### electron/preload.js

```javascript
import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  createWebview: (options) => ipcRenderer.invoke('create-webview', options),
  hideWebview: (id) => ipcRenderer.invoke('hide-webview', id),
  showWebview: (id) => ipcRenderer.invoke('show-webview', id),
  closeWebview: (id) => ipcRenderer.invoke('close-webview', id),
  setWebviewBounds: (id, bounds) => ipcRenderer.invoke('set-webview-bounds', id, bounds)
});
```

### src/components/WebView.tsx (Electron version)

```tsx
import { useEffect, useRef } from 'react';
import { useAppStore } from '../store';

const webviewInstances = new Set<string>();

export const WebView = () => {
  const { activePlatform, activeMode } = useAppStore();
  const previousPlatformId = useRef<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateWebviewBounds = async () => {
    if (!activePlatform || !containerRef.current || !window.electronAPI) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    await window.electronAPI.setWebviewBounds(activePlatform.id, {
      x: Math.round(rect.left),
      y: Math.round(rect.top),
      width: Math.round(rect.width),
      height: Math.round(rect.height)
    });
  };

  useEffect(() => {
    const manageWebviews = async () => {
      if (!window.electronAPI) return;

      if (previousPlatformId.current) {
        await window.electronAPI.hideWebview(previousPlatformId.current);
      }

      if (!activePlatform) {
        previousPlatformId.current = null;
        return;
      }

      const { id, url, name } = activePlatform;

      if (webviewInstances.has(id)) {
        await updateWebviewBounds();
        await window.electronAPI.showWebview(id);
      } else {
        await window.electronAPI.createWebview({ url, title: name, id });
        webviewInstances.add(id);
        setTimeout(updateWebviewBounds, 100);
      }

      previousPlatformId.current = id;
    };

    if (activeMode !== 'settings') {
      manageWebviews();
    }
  }, [activePlatform, activeMode]);

  useEffect(() => {
    if (activeMode === 'settings' && window.electronAPI) {
      webviewInstances.forEach(id => {
        window.electronAPI.hideWebview(id);
      });
      previousPlatformId.current = null;
    }
  }, [activeMode]);

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary h-full">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div ref={containerRef} className="flex-1 relative">
      {!activePlatform && <EmptyState />}
    </div>
  );
};

declare global {
  interface Window {
    electronAPI: {
      createWebview: (options: { url: string; title: string; id: string }) => Promise<void>;
      hideWebview: (id: string) => Promise<void>;
      showWebview: (id: string) => Promise<void>;
      closeWebview: (id: string) => Promise<void>;
      setWebviewBounds: (id: string, bounds: { x: number; y: number; width: number; height: number }) => Promise<void>;
    };
  }
}
```