# Electron White Screen Fix - Complete Diagnostic & Solution Plan

## Problem Analysis
Based on your codebase migration from Tauri to Electron, you're experiencing a white screen issue. This is typically caused by:
1. Content Security Policy (CSP) restrictions
2. Module loading/bundling issues
3. Path resolution problems
4. Renderer process initialization failures
5. Missing development environment detection

## Step 1: Fix Main Process Issues

### electron/main.js
```javascript
import { app, BrowserWindow, shell, ipcMain, session } from 'electron';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Add this for better debugging
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
const isDev = !app.isPackaged;

let mainWindow;
const webviewWindows = new Map();

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // Disable for development
      allowRunningInsecureContent: true,
      experimentalFeatures: true
    },
    titleBarStyle: 'default',
    show: false,
    icon: path.join(__dirname, '../public/icons/openai.svg') // Add app icon
  });

  // Enhanced loading logic
  if (isDev) {
    console.log('Development mode: Loading from localhost');
    mainWindow.loadURL('http://localhost:1420').catch(err => {
      console.error('Failed to load dev server:', err);
      // Fallback to file loading if dev server isn't running
      const indexPath = path.join(__dirname, '../dist/index.html');
      console.log('Fallback: Loading from file:', indexPath);
      mainWindow.loadFile(indexPath);
    });
    
    // Auto-open DevTools in development
    mainWindow.webContents.once('dom-ready', () => {
      mainWindow.webContents.openDevTools();
    });
  } else {
    const indexPath = path.join(__dirname, '../dist/index.html');
    console.log('Production mode: Loading from file:', indexPath);
    mainWindow.loadFile(indexPath);
  }

  // Enhanced error handling
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', {
      errorCode,
      errorDescription,
      validatedURL
    });
  });

  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('Renderer process crashed:', { killed });
  });

  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show');
    mainWindow.show();
    
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  mainWindow.on('closed', () => {
    webviewWindows.forEach(window => {
      try {
        window.close();
      } catch (err) {
        console.error('Error closing webview:', err);
      }
    });
    webviewWindows.clear();
    mainWindow = null;
  });
}

// Enhanced webview creation with better error handling
function createWebviewWindow(url, title, id) {
  console.log(`Creating webview for ${title} (${id}): ${url}`);
  
  if (webviewWindows.has(id)) {
    const existingWindow = webviewWindows.get(id);
    existingWindow.show();
    existingWindow.focus();
    return;
  }

  const webviewWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    parent: mainWindow,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
      allowRunningInsecureContent: true,
      partition: `persist:webview-${id}` // Separate session for each webview
    },
    titleBarStyle: 'default',
    frame: false,
    skipTaskbar: true,
    title: title
  });

  // Load URL with error handling
  webviewWindow.loadURL(url).catch(err => {
    console.error(`Failed to load URL ${url}:`, err);
  });
  
  webviewWindow.once('ready-to-show', () => {
    console.log(`Webview ${id} ready to show`);
    webviewWindow.show();
  });

  // Handle external links
  webviewWindow.webContents.setWindowOpenHandler(({ url: linkUrl }) => {
    console.log(`Opening external URL: ${linkUrl}`);
    shell.openExternal(linkUrl);
    return { action: 'deny' };
  });

  // Handle navigation
  webviewWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    console.log(`Navigating to: ${navigationUrl}`);
    // Allow navigation within the same domain
  });

  webviewWindow.on('closed', () => {
    console.log(`Webview ${id} closed`);
    webviewWindows.delete(id);
  });

  webviewWindows.set(id, webviewWindow);
  return webviewWindow;
}

app.whenReady().then(async () => {
  console.log('App ready, setting up...');
  
  // Remove default CSP to avoid loading issues
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ['default-src * \'unsafe-inline\' \'unsafe-eval\' data: blob:;']
      }
    });
  });

  // Additional session setup
  session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
    details.requestHeaders['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Electron/28.0.0';
    callback({ cancel: false, requestHeaders: details.requestHeaders });
  });

  createMainWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Enhanced IPC handlers with logging
ipcMain.handle('create-webview', async (event, { url, title, id }) => {
  console.log(`IPC: create-webview`, { url, title, id });
  try {
    return createWebviewWindow(url, title, id);
  } catch (error) {
    console.error('Error creating webview:', error);
    throw error;
  }
});

ipcMain.handle('hide-webview', async (event, id) => {
  console.log(`IPC: hide-webview`, id);
  const window = webviewWindows.get(id);
  if (window) {
    window.hide();
  }
});

ipcMain.handle('show-webview', async (event, id) => {
  console.log(`IPC: show-webview`, id);
  const window = webviewWindows.get(id);
  if (window) {
    window.show();
    window.focus();
  }
});

ipcMain.handle('close-webview', async (event, id) => {
  console.log(`IPC: close-webview`, id);
  const window = webviewWindows.get(id);
  if (window) {
    window.close();
  }
});

ipcMain.handle('set-webview-bounds', async (event, id, bounds) => {
  console.log(`IPC: set-webview-bounds`, id, bounds);
  const window = webviewWindows.get(id);
  if (window && !window.isDestroyed()) {
    try {
      window.setBounds(bounds);
    } catch (error) {
      console.error('Error setting webview bounds:', error);
    }
  }
});

// Add app info for debugging
ipcMain.handle('get-app-info', async () => {
  return {
    version: app.getVersion(),
    isDev,
    platform: process.platform,
    arch: process.arch
  };
});
```

## Step 2: Fix Preload Script

### electron/preload.js
```javascript
const { contextBridge, ipcRenderer } = require('electron');

console.log('Preload script loading...');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  createWebview: (options) => {
    console.log('Preload: createWebview called', options);
    return ipcRenderer.invoke('create-webview', options);
  },
  hideWebview: (id) => {
    console.log('Preload: hideWebview called', id);
    return ipcRenderer.invoke('hide-webview', id);
  },
  showWebview: (id) => {
    console.log('Preload: showWebview called', id);
    return ipcRenderer.invoke('show-webview', id);
  },
  closeWebview: (id) => {
    console.log('Preload: closeWebview called', id);
    return ipcRenderer.invoke('close-webview', id);
  },
  setWebviewBounds: (id, bounds) => {
    console.log('Preload: setWebviewBounds called', id, bounds);
    return ipcRenderer.invoke('set-webview-bounds', id, bounds);
  },
  getAppInfo: () => {
    return ipcRenderer.invoke('get-app-info');
  }
});

// Add window loaded event for debugging
window.addEventListener('DOMContentLoaded', () => {
  console.log('Preload: DOM Content Loaded');
});

console.log('Preload script loaded successfully');
```

## Step 3: Fix Package.json Scripts

### package.json (Update scripts section)
```json
{
  "name": "switch-ai-electron",
  "private": true,
  "version": "0.1.0",
  "type": "module",
  "main": "electron/main.js",
  "homepage": "./",
  "scripts": {
    "dev": "concurrently --kill-others \"npm run dev:vite\" \"npm run dev:electron\"",
    "dev:vite": "vite --host localhost --port 1420",
    "dev:electron": "wait-on http://localhost:1420 && NODE_ENV=development electron .",
    "build": "npm run build:renderer && npm run build:electron",
    "build:renderer": "tsc && vite build",
    "build:electron": "electron-builder",
    "preview": "vite preview",
    "postinstall": "electron-builder install-app-deps"
  },
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "zustand": "^5.0.7"
  },
  "devDependencies": {
    "@types/react": "^18.3.1",
    "@types/react-dom": "^18.3.1",
    "@vitejs/plugin-react": "^4.3.4",
    "autoprefixer": "^10.4.21",
    "concurrently": "^8.2.2",
    "electron": "^28.0.0",
    "electron-builder": "^24.6.4",
    "postcss": "^8.5.6",
    "tailwindcss": "^3.4.17",
    "typescript": "~5.6.2",
    "vite": "^6.0.3",
    "wait-on": "^7.2.0"
  },
  "build": {
    "appId": "com.switchai.app",
    "productName": "Switch.AI",
    "directories": {
      "output": "dist-electron"
    },
    "files": [
      "dist/**/*",
      "electron/**/*",
      "!electron/node_modules",
      "node_modules/**/*"
    ],
    "extraResources": [
      "public/**/*"
    ],
    "mac": {
      "identity": null,
      "target": "dmg"
    },
    "win": {
      "target": "nsis"
    },
    "linux": {
      "target": "AppImage"
    }
  }
}
```

## Step 4: Fix Vite Configuration

### vite.config.ts
```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  base: "./", // Important for Electron
  server: {
    port: 1420,
    strictPort: true,
    host: 'localhost'
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      }
    }
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
```

## Step 5: Fix Index.html

### index.html
```html
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/icons/openai.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src *;">
    <title>Switch.AI</title>
    <style>
      /* Prevent flash of unstyled content */
      body {
        margin: 0;
        padding: 0;
        font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #0a0a0a;
        color: #ffffff;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
      }
      
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100vw;
        height: 100vh;
        background-color: #0a0a0a;
        color: #ffffff;
        font-size: 18px;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">Loading Switch.AI...</div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
```

## Step 6: Add Error Boundary

### src/components/ErrorBoundary.tsx
```tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="h-screen flex items-center justify-center bg-bgPrimary text-textPrimary">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-red-400">Something went wrong</h1>
            <p className="text-textSecondary">Please restart the application</p>
            <pre className="text-xs bg-bgSecondary p-4 rounded max-w-md overflow-auto">
              {this.state.error?.message}
            </pre>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-accentStart text-white rounded hover:bg-accentHover"
            >
              Reload App
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Step 7: Update App.tsx with Error Boundary

### src/App.tsx
```tsx
import { useEffect } from 'react';
import { TopBar } from './components/TopBar';
import { Sidebar } from './components/Sidebar';
import { WebView } from './components/WebView';
import { Settings } from './pages/Settings';
import { useAppStore } from './store';
import { ErrorBoundary } from './components/ErrorBoundary';

export default function App() {
  const { activeMode } = useAppStore();

  useEffect(() => {
    console.log('App component mounted');
    
    // Check if we're in Electron
    if (window.electronAPI) {
      console.log('Electron API available');
      window.electronAPI.getAppInfo?.().then(info => {
        console.log('App info:', info);
      });
    } else {
      console.warn('Electron API not available');
    }
  }, []);

  return (
    <ErrorBoundary>
      <div className="h-screen flex flex-col bg-bgPrimary">
        <TopBar />
        <div className="flex-1 flex overflow-hidden">
          {activeMode === 'settings' ? (
            <div className="flex-1 overflow-y-auto">
              <Settings />
            </div>
          ) : (
            <>
              <Sidebar />
              <WebView />
            </>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}
```

## Step 8: Debug Commands to Run

```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Development mode with debugging
npm run dev

# If development fails, try building and running
npm run build
npm run build:electron

# Check Electron logs
DEBUG=* npm run dev:electron
```

## Step 9: Common Troubleshooting Checklist

1. **Check Console Logs**: Open DevTools in both main window and check terminal output
2. **Verify File Paths**: Ensure all file paths in main.js are correct
3. **Check Network Tab**: Verify all assets are loading correctly
4. **Test CSP**: Temporarily disable CSP if issues persist
5. **Check Electron Version**: Ensure compatibility between Electron and Node versions
6. **Verify Preload**: Confirm electronAPI is exposed to renderer process

This masterplan addresses the most common causes of white screen issues in Electron applications, with enhanced debugging capabilities and proper error handling throughout the stack.