# 🚀 Switch.AI - AI Platform Browser

A simple desktop app to quickly access 40+ AI platforms in one place.

## 🎯 Features

- **4 Modes**: <PERSON><PERSON>, <PERSON>reate, Lab, Hub
- **48 AI Platforms**: ChatGPT, Claude, Midjourney, and more
- **Quick Switching**: Click icons to open platforms in new windows
- **Dark Theme**: Purple gradient design
- **Desktop Only**: macOS (Intel/Silicon), Windows, and Linux

## 🏗️ For Developers

### Quick Start
```bash
npm install
npm run dev
```

### Build for Friends
```bash
npm run build
```

**Output**: Built application in `dist/` directory

### Share Instructions
1. **macOS**: Right-click the `.app` → Open (first time only)
2. **Windows**: Run the `.exe` installer

## 📁 Project Structure

```
src/
├── components/     # UI components
├── data/          # 48 platforms array
├── store/         # Zustand state
├── styles/        # Tailwind CSS
└── types/         # TypeScript interfaces
```

## 🎨 Adding Platforms

Edit `src/data/platforms.ts`:
```ts
{
  id: 'newai',
  mode: 'chat',
  name: 'New AI',
  url: 'https://newai.com',
  iconFile: 'newai.svg'
}
```

Add the icon to `public/icons/newai.svg`.

## 🔧 Tech Stack

- **Electron** - Desktop framework
- **React + TypeScript** - Frontend
- **Tailwind CSS** - Styling
- **Zustand** - State management

## 🐧 Building for Linux (Debian/Ubuntu)

### 1. Install Node.js and npm
Ensure you have a recent version of Node.js (v18+ recommended) and npm.

### 2. Build the App
Once all dependencies are installed, you can build the application:

```bash
npm install
npm run build
```

The compiled application will be built using Electron Builder.

---

**Note**: This is a hobby project for friends. No signing, no app store, no CI/CD. Just build and share! 🎉
