import { app, BrowserWindow, shell, ipcMain, session } from 'electron';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let mainWindow;
const webviewWindows = new Map();

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    titleBarStyle: 'default',
    show: false
  });

  const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

  if (isDev) {
    mainWindow.loadURL('http://localhost:1420');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    webviewWindows.forEach(window => window.close());
    webviewWindows.clear();
  });
}

function createWebviewWindow(url, title, id) {
  if (webviewWindows.has(id)) {
    const existingWindow = webviewWindows.get(id);
    existingWindow.show();
    existingWindow.focus();
    return;
  }

  const webviewWindow = new BrowserWindow({
    width: 800,
    height: 600,
    parent: mainWindow,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
      allowRunningInsecureContent: true
    },
    titleBarStyle: 'default',
    frame: false,
    skipTaskbar: true
  });

  webviewWindow.loadURL(url);
  
  webviewWindow.once('ready-to-show', () => {
    webviewWindow.show();
  });

  webviewWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  webviewWindow.on('closed', () => {
    webviewWindows.delete(id);
  });

  webviewWindows.set(id, webviewWindow);
  return webviewWindow;
}

app.whenReady().then(() => {
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ['default-src * data: blob: filesystem: about: ws: wss: \'unsafe-inline\' \'unsafe-eval\'; script-src * data: blob: \'unsafe-inline\' \'unsafe-eval\'; connect-src * data: blob: \'unsafe-inline\'; img-src * data: blob: \'unsafe-inline\'; frame-src * data: blob:; style-src * data: blob: \'unsafe-inline\'; font-src * data: blob: \'unsafe-inline\';']
      }
    });
  });

  createMainWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createMainWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

ipcMain.handle('create-webview', (event, { url, title, id }) => {
  return createWebviewWindow(url, title, id);
});

ipcMain.handle('hide-webview', (event, id) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.hide();
  }
});

ipcMain.handle('show-webview', (event, id) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.show();
    window.focus();
  }
});

ipcMain.handle('close-webview', (event, id) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.close();
  }
});

ipcMain.handle('set-webview-bounds', (event, id, bounds) => {
  const window = webviewWindows.get(id);
  if (window) {
    window.setBounds(bounds);
  }
});
