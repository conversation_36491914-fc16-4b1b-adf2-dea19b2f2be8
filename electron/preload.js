import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  createWebview: (options) => ipcRenderer.invoke('create-webview', options),
  hideWebview: (id) => ipcRenderer.invoke('hide-webview', id),
  showWebview: (id) => ipcRenderer.invoke('show-webview', id),
  closeWebview: (id) => ipcRenderer.invoke('close-webview', id),
  setWebviewBounds: (id, bounds) => ipcRenderer.invoke('set-webview-bounds', id, bounds)
});
