{"name": "switch-ai-electron", "private": true, "version": "0.1.0", "type": "module", "main": "electron/main.js", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:1420 && electron .", "build": "tsc && vite build && electron-builder", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.6.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3", "wait-on": "^7.2.0"}, "build": {"appId": "com.switchai.app", "productName": "Switch.AI", "directories": {"output": "dist"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"identity": null}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}