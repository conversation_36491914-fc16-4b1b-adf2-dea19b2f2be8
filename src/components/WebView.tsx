import { useEffect, useRef } from 'react';
import { useAppStore } from '../store';

const webviewInstances = new Set<string>();

export const WebView = () => {
  const { activePlatform, activeMode } = useAppStore();
  const previousPlatformId = useRef<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateWebviewBounds = async () => {
    if (!activePlatform || !containerRef.current || !window.electronAPI) return;

    const rect = containerRef.current.getBoundingClientRect();
    await window.electronAPI.setWebviewBounds(activePlatform.id, {
      x: Math.round(rect.left),
      y: Math.round(rect.top),
      width: Math.round(rect.width),
      height: Math.round(rect.height)
    });
  };

  useEffect(() => {
    const manageWebviews = async () => {
      if (!window.electronAPI) return;

      if (previousPlatformId.current) {
        await window.electronAPI.hideWebview(previousPlatformId.current);
      }

      if (!activePlatform) {
        previousPlatformId.current = null;
        return;
      }

      const { id, url, name } = activePlatform;

      if (webviewInstances.has(id)) {
        await updateWebviewBounds();
        await window.electronAPI.showWebview(id);
      } else {
        await window.electronAPI.createWebview({ url, title: name, id });
        webviewInstances.add(id);
        setTimeout(updateWebviewBounds, 100);
      }

      previousPlatformId.current = id;

    };

    if (activeMode !== 'settings') {
      manageWebviews();
    }
  }, [activePlatform, activeMode]);

  useEffect(() => {
    if (activeMode === 'settings' && window.electronAPI) {
      webviewInstances.forEach(id => {
        window.electronAPI.hideWebview(id);
      });
      previousPlatformId.current = null;
    }
  }, [activeMode]);



  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary h-full">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin.
          </p>
        </div>
        {activePlatform && (
          <button
            onClick={() => window.open(activePlatform.url, '_blank')}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd hover:from-accentHover hover:to-accentStart text-white rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div ref={containerRef} className="flex-1 relative">
      {!activePlatform && <EmptyState />}
    </div>
  );
};

declare global {
  interface Window {
    electronAPI: {
      createWebview: (options: { url: string; title: string; id: string }) => Promise<void>;
      hideWebview: (id: string) => Promise<void>;
      showWebview: (id: string) => Promise<void>;
      closeWebview: (id: string) => Promise<void>;
      setWebviewBounds: (id: string, bounds: { x: number; y: number; width: number; height: number }) => Promise<void>;
    };
  }
}
